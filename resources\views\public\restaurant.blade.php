<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $restaurant['name'] }} - QR Menu</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #ff6b6b;
            --secondary-color: #4ecdc4;
            --text-color: #333;
            --bg-color: #f8f9fa;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        /* Header */
        .header {
            background: var(--primary-color);
            color: white;
            padding: 1rem;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .branch-selector {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .branch-selector option {
            background: var(--primary-color);
            color: white;
        }

        .cart-icon {
            position: relative;
            cursor: pointer;
            font-size: 1.5rem;
        }

        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: white;
            color: var(--primary-color);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: bold;
        }

        /* Main Container */
        .main-container {
            margin-top: 80px;
            padding: 1rem;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Restaurant Info */
        .restaurant-info {
            background: white;
            padding: 1rem;
            border-radius: 10px;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
        }

        .restaurant-info h1 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .restaurant-info p {
            color: #666;
            margin-bottom: 0.25rem;
        }

        /* Events Carousel */
        .events-section {
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-color);
        }

        .carousel {
            position: relative;
            overflow: hidden;
            border-radius: 10px;
            box-shadow: var(--shadow);
            background: white;
        }

        .carousel-inner {
            display: flex;
            transition: transform 0.3s ease;
        }

        .carousel-item {
            min-width: 100%;
            position: relative;
        }

        .carousel-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .carousel-caption {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 1rem;
        }

        .carousel-controls {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 0.5rem 1rem;
            cursor: pointer;
            font-size: 1.2rem;
            border-radius: 50%;
            transition: background 0.3s;
        }

        .carousel-controls:hover {
            background: white;
        }

        .carousel-prev {
            left: 10px;
        }

        .carousel-next {
            right: 10px;
        }

        /* Category Slider */
        .category-slider {
            display: flex;
            overflow-x: auto;
            gap: 1rem;
            padding: 0.5rem 0;
            margin-bottom: 2rem;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .category-slider::-webkit-scrollbar {
            display: none;
        }

        .category-item {
            background: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: var(--shadow);
            border: 2px solid transparent;
        }

        .category-item.active {
            background: var(--primary-color);
            color: white;
            transform: scale(1.05);
        }

        .category-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        /* Menu Items - Smaller Cards */
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .menu-item {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: transform 0.3s;
            cursor: pointer;
        }

        .menu-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .menu-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }

        .menu-item-content {
            padding: 0.75rem;
        }

        .menu-item-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 0.5rem;
        }

        .menu-item-title {
            font-size: 1rem;
            font-weight: bold;
            color: var(--text-color);
            line-height: 1.2;
        }

        .menu-item-price {
            color: var(--primary-color);
            font-weight: bold;
            font-size: 0.9rem;
            white-space: nowrap;
            margin-left: 0.5rem;
        }

        .menu-item-description {
            color: #666;
            font-size: 0.8rem;
            margin-bottom: 0.75rem;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .menu-item-category {
            color: var(--secondary-color);
            font-size: 0.7rem;
            text-transform: uppercase;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .add-to-cart {
            width: 100%;
            padding: 0.6rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s;
        }

        .add-to-cart:hover {
            background: #ff5252;
        }

        /* Cart Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
            animation: fadeIn 0.3s;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 10px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        .close {
            font-size: 1.5rem;
            cursor: pointer;
            color: #999;
        }

        .close:hover {
            color: var(--text-color);
        }

        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #eee;
        }

        .cart-item-info {
            flex: 1;
        }

        .cart-item-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .cart-item-price {
            color: var(--primary-color);
        }

        .cart-item-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .quantity-btn {
            background: #f0f0f0;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
        }

        .cart-total {
            text-align: right;
            margin-top: 1rem;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .checkout-btn {
            width: 100%;
            padding: 1rem;
            background: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1.1rem;
            margin-top: 1rem;
        }

        .checkout-btn:hover {
            background: #45b7aa;
        }

        /* Waiter Request Button */
        .waiter-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: transform 0.3s;
        }

        .waiter-btn:hover {
            transform: scale(1.1);
        }

        /* Waiter Request Modal */
        .waiter-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
            animation: fadeIn 0.3s;
        }

        .waiter-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 10px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .waiter-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        .tables-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .table-option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .table-option:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .table-option.selected {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .table-number {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .table-area {
            font-size: 0.8rem;
            color: #666;
        }

        .table-option.selected .table-area {
            color: rgba(255,255,255,0.8);
        }

        .request-waiter-btn {
            width: 100%;
            padding: 1rem;
            background: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1.1rem;
            transition: background 0.3s;
        }

        .request-waiter-btn:hover {
            background: #45b7aa;
        }

        .request-waiter-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .menu-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            }
            
            .carousel-item img {
                height: 150px;
            }

            .header {
                padding: 0.75rem;
            }

            .header-content {
                flex-direction: row;
                flex-wrap: wrap;
                gap: 0.5rem;
                align-items: center;
            }

            .logo {
                font-size: 1.2rem;
                flex: 1;
                min-width: 0;
            }

            .header-controls {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                flex-shrink: 0;
            }

            .branch-selector {
                padding: 0.4rem 0.8rem;
                font-size: 0.8rem;
                min-width: 120px;
            }

            .main-container {
                margin-top: 90px;
            }

            .tables-grid {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                gap: 0.75rem;
            }

            .table-option {
                padding: 0.75rem;
            }
        }

        @media (max-width: 480px) {
            .menu-grid {
                grid-template-columns: 1fr 1fr;
            }
            
            .menu-item-content {
                padding: 0.5rem;
            }
            
            .menu-item-title {
                font-size: 0.9rem;
            }
            
            .menu-item-price {
                font-size: 0.8rem;
            }

            .header {
                padding: 0.5rem;
            }

            .header-content {
                flex-direction: column;
                align-items: stretch;
                gap: 0.5rem;
            }

            .logo {
                text-align: center;
                font-size: 1.1rem;
            }

            .header-controls {
                justify-content: space-between;
                width: 100%;
            }

            .branch-selector {
                flex: 1;
                margin-right: 1rem;
            }

            .main-container {
                margin-top: 100px;
            }

            .tables-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            .waiter-modal-content {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">🍽️ {{ $restaurant['name'] }}</div>
            <div class="header-controls">
                @if(count($branches) > 1)
                <select class="branch-selector" onchange="changeBranch(this.value)">
                    @foreach($branches as $branch)
                        <option value="{{ $branch->id }}" {{ $branch->id == $restaurant['branch_id'] ? 'selected' : '' }}>
                            {{ $branch->name }}
                        </option>
                    @endforeach
                </select>
                @endif
                <div class="cart-icon" onclick="toggleCart()">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count" id="cartCount">0</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Restaurant Info -->
        <section class="restaurant-info">
            <h1>{{ $restaurant['name'] }}</h1>
            <p><i class="fas fa-map-marker-alt"></i> {{ $restaurant['business_address'] }}</p>
            <p><i class="fas fa-phone"></i> {{ $restaurant['contact_phone'] }}</p>
            <p><i class="fas fa-clock"></i> Business Hours: {{ $restaurant['business_hours'] ?? 'Contact for hours' }}</p>
        </section>

        @if(isset($restaurant['current_table']))
        <!-- Current Table Info -->
        <section class="restaurant-info" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin-bottom: 2rem;">
            <div style="display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap;">
                <div>
                    <h2 style="color: white; margin-bottom: 0.5rem; display: flex; align-items: center;">
                        <i class="fas fa-chair" style="margin-right: 0.5rem;"></i>
                        You're seated at Table {{ $restaurant['current_table']['table_number'] }}
                    </h2>
                    @if($restaurant['current_table']['table_name'])
                        <p style="margin-bottom: 0.5rem; opacity: 0.9;">
                            <i class="fas fa-tag"></i> {{ $restaurant['current_table']['table_name'] }}
                        </p>
                    @endif
                    @if($restaurant['current_table']['area_name'])
                        <p style="margin-bottom: 0; opacity: 0.9;">
                            <i class="fas fa-map-marker-alt"></i> {{ $restaurant['current_table']['area_name'] }} Area
                        </p>
                    @endif
                </div>
                <div style="text-align: center; margin-top: 1rem;">
                    <div style="background: rgba(255,255,255,0.2); padding: 0.75rem 1.5rem; border-radius: 25px; display: inline-block;">
                        <i class="fas fa-qrcode" style="margin-right: 0.5rem;"></i>
                        QR Code Access
                    </div>
                </div>
            </div>
        </section>
        @endif

        <!-- Events Carousel -->
        <section class="events-section">
            <h2 class="section-title">Special Events</h2>
            <div class="carousel">
                <div class="carousel-inner" id="carouselInner">
                    <div class="carousel-item">
                        <img src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=800" alt="Wine Night">
                        <div class="carousel-caption">
                            <h3>Weekly Wine Night</h3>
                            <p>Every Friday - 50% off on selected wines</p>
                        </div>
                    </div>
                    <div class="carousel-item">
                        <img src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=800" alt="Seafood Special">
                        <div class="carousel-caption">
                            <h3>Seafood Weekend</h3>
                            <p>Fresh catch every Saturday & Sunday</p>
                        </div>
                    </div>
                    <div class="carousel-item">
                        <img src="https://images.unsplash.com/photo-1551782450-a2132b4ba21d?w=800" alt="Live Music">
                        <div class="carousel-caption">
                            <h3>Live Jazz Nights</h3>
                            <p>Every Thursday from 7 PM</p>
                        </div>
                    </div>
                </div>
                <button class="carousel-controls carousel-prev" onclick="prevSlide()">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="carousel-controls carousel-next" onclick="nextSlide()">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </section>

        <!-- Category Slider -->
        <section>
            <h2 class="section-title">Categories</h2>
            <div class="category-slider" id="categorySlider">
                <div class="category-item active" data-category="all">All Items</div>
                @foreach($categories as $category)
                    <div class="category-item" data-category="{{ $category->id }}">{{ $category->name }}</div>
                @endforeach
            </div>
        </section>

        <!-- Menu Items -->
        <section>
            <h2 class="section-title">Menu</h2>
            <div class="menu-grid" id="menuGrid">
                @foreach($menuItems as $item)
                    <div class="menu-item" data-category="{{ $item->category->id }}">
                        <img src="{{ $item->image ?? asset('assets/images/product1.jpg') }}" alt="{{ $item->name }}" onerror="this.src='{{ asset('assets/images/product1.jpg') }}'">
                        <div class="menu-item-content">
                            <div class="menu-item-category">{{ $item->category->name }}</div>
                            <div class="menu-item-header">
                                <h3 class="menu-item-title">{{ $item->name }}</h3>
                                <span class="menu-item-price">${{ number_format($item->getBranchPrice($restaurant['branch_id']), 2) }}</span>
                            </div>
                            <p class="menu-item-description">{{ $item->description ?? 'Delicious menu item' }}</p>
                            <button class="add-to-cart" onclick="addToCart({{ $item->id }}, '{{ $item->name }}', {{ $item->getBranchPrice($restaurant['branch_id']) }}, '{{ $item->category->name }}')">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                @endforeach
            </div>
        </section>
    </div>

    <!-- Cart Modal -->
    <div class="modal" id="cartModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Your Cart</h2>
                <span class="close" onclick="toggleCart()">&times;</span>
            </div>
            <div id="cartItems"></div>
            <div class="cart-total" id="cartTotal">Total: $0.00</div>
            <button class="checkout-btn" onclick="submitOrder()">Submit Order</button>
        </div>
    </div>

    <!-- Waiter Request Modal -->
    <div class="waiter-modal" id="waiterModal">
        <div class="waiter-modal-content">
            <div class="waiter-modal-header">
                <h2>Request Waiter</h2>
                <span class="close" onclick="toggleWaiterModal()">&times;</span>
            </div>
            <p style="margin-bottom: 1.5rem; color: #666;">Please select your table to request waiter assistance:</p>
            <div class="tables-grid" id="tablesGrid">
                @foreach($tables as $table)
                    <div class="table-option" data-table-id="{{ $table->id }}" onclick="selectTable({{ $table->id }})">
                        <div class="table-number">{{ $table->table_number }}</div>
                        <div class="table-area">{{ $table->area->name ?? 'No Area' }}</div>
                    </div>
                @endforeach
            </div>
            <button class="request-waiter-btn" id="requestWaiterBtn" onclick="submitWaiterRequest()" disabled>
                Request Waiter
            </button>
        </div>
    </div>

    <!-- Waiter Request Button -->
    <button class="waiter-btn" onclick="callWaiter()">
        <i class="fas fa-bell"></i>
    </button>

    <script>
        // Restaurant data from backend
        const restaurantData = {
            name: '{{ $restaurant["name"] }}',
            tenantName: '{{ $tenantName }}',
            branchId: {{ $restaurant['branch_id'] }},
            tables: @json($tables),
            areas: @json($areas)
        };

        // Cart functionality
        let cart = [];
        let currentCategory = 'all';
        let currentSlide = 0;
        let selectedTableId = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            startCarousel();
            setupCategoryFiltering();
        });

        // Branch change functionality
        function changeBranch(branchId) {
            // Extract tenant name from current URL
            const pathParts = window.location.pathname.split('/');
            const tenantName = pathParts[pathParts.indexOf('restaurant') + 1] || restaurantData.tenantName;
            
            // Construct new URL with branch parameter
            const newUrl = `/restaurant/${tenantName}?branch_id=${branchId}`;
            window.location.href = newUrl;
        }

        // Category filtering
        function setupCategoryFiltering() {
            document.querySelectorAll('.category-item').forEach(item => {
                item.addEventListener('click', function() {
                    document.querySelector('.category-item.active').classList.remove('active');
                    this.classList.add('active');
                    currentCategory = this.dataset.category;
                    filterMenuItems();
                });
            });
        }

        function filterMenuItems() {
            const menuItems = document.querySelectorAll('.menu-item');
            menuItems.forEach(item => {
                if (currentCategory === 'all' || item.dataset.category === currentCategory) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // Cart functions
        function addToCart(itemId, itemName, itemPrice, categoryName) {
            const existingItem = cart.find(c => c.id === itemId);
            
            if (existingItem) {
                existingItem.quantity++;
            } else {
                cart.push({
                    id: itemId,
                    name: itemName,
                    price: itemPrice,
                    category: categoryName,
                    quantity: 1
                });
            }
            
            updateCartCount();
            showNotification(`${itemName} added to cart!`);
        }

        function updateCartCount() {
            const count = cart.reduce((sum, item) => sum + item.quantity, 0);
            document.getElementById('cartCount').textContent = count;
        }

        function toggleCart() {
            const modal = document.getElementById('cartModal');
            if (modal.style.display === 'block') {
                modal.style.display = 'none';
            } else {
                renderCart();
                modal.style.display = 'block';
            }
        }

        function renderCart() {
            const cartItems = document.getElementById('cartItems');
            const cartTotal = document.getElementById('cartTotal');
            
            if (cart.length === 0) {
                cartItems.innerHTML = '<p>Your cart is empty</p>';
                cartTotal.textContent = 'Total: $0.00';
                return;
            }
            
            cartItems.innerHTML = cart.map(item => `
                <div class="cart-item">
                    <div class="cart-item-info">
                        <div class="cart-item-title">${item.name}</div>
                        <div class="cart-item-price">$${item.price.toFixed(2)}</div>
                    </div>
                    <div class="cart-item-controls">
                        <button class="quantity-btn" onclick="updateQuantity(${item.id}, -1)">-</button>
                        <span>${item.quantity}</span>
                        <button class="quantity-btn" onclick="updateQuantity(${item.id}, 1)">+</button>
                    </div>
                </div>
            `).join('');
            
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            cartTotal.textContent = `Total: $${total.toFixed(2)}`;
        }

        function updateQuantity(itemId, change) {
            const item = cart.find(c => c.id === itemId);
            item.quantity += change;
            
            if (item.quantity <= 0) {
                cart = cart.filter(c => c.id !== itemId);
            }
            
            updateCartCount();
            renderCart();
        }

        async function submitOrder() {
            if (cart.length === 0) {
                showNotification('Your cart is empty!', 'error');
                return;
            }

            // Show table selection modal first if no table selected
            if (!selectedTableId) {
                toggleWaiterModal();
                // Store the action to perform after table selection
                window.pendingAction = 'submitOrder';
                return;
            }

            const submitBtn = document.querySelector('.checkout-btn');
            submitBtn.disabled = true;
            submitBtn.textContent = 'Submitting...';

            try {
                // Extract tenant name from current URL
                const pathParts = window.location.pathname.split('/');
                const tenantName = pathParts[pathParts.indexOf('restaurant') + 1] || restaurantData.tenantName;
                
                // Prepare order data (similar to POS)
                const orderData = {
                    table_id: selectedTableId,
                    branch_id: restaurantData.branchId,
                    order_type: 'dine_in',
                    status: 'pending', // Save as pending like POS
                    items: cart.map(item => ({
                        menu_item_id: item.id,
                        quantity: item.quantity,
                        unit_price: item.price,
                        special_instructions: null
                    })),
                    customer_name: null,
                    customer_phone: null,
                    special_instructions: 'Order submitted from public menu',
                    _token: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                };
                
                console.log('Submitting order:', orderData);
                
                const response = await fetch(`/restaurant/${tenantName}/submit-order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    },
                    body: JSON.stringify(orderData)
                });

                console.log('Response status:', response.status);
                const result = await response.json();
                console.log('Response data:', result);

                if (response.ok && result.success) {
                    const selectedTable = restaurantData.tables.find(t => t.id === selectedTableId);
                    const tableNumber = selectedTable ? selectedTable.table_number : selectedTableId;
                    
                    showNotification(`Order submitted successfully for table ${tableNumber}! Order ID: ${result.order_number}`, 'success');
                    
                    // Clear cart
                    cart = [];
                    updateCartCount();
                    toggleCart();
                    
                    // Reset table selection
                    selectedTableId = null;
                    updateTableSelection();
                } else {
                    throw new Error(result.message || result.error || 'Failed to submit order');
                }
            } catch (error) {
                console.error('Error submitting order:', error);
                showNotification('Failed to submit order. Please try again. Error: ' + error.message, 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Submit Order';
            }
        }

        function callWaiter() {
            toggleWaiterModal();
        }

        function toggleWaiterModal() {
            const modal = document.getElementById('waiterModal');
            if (modal.style.display === 'block') {
                modal.style.display = 'none';
                // Only reset table selection if not used for order submission
                if (!window.pendingAction) {
                    selectedTableId = null;
                    updateTableSelection();
                }
            } else {
                modal.style.display = 'block';
            }
        }

        function selectTable(tableId) {
            selectedTableId = tableId;
            updateTableSelection();
        }

        function updateTableSelection() {
            // Remove selected class from all tables
            document.querySelectorAll('.table-option').forEach(table => {
                table.classList.remove('selected');
            });

            // Add selected class to chosen table
            if (selectedTableId) {
                const selectedTable = document.querySelector(`[data-table-id="${selectedTableId}"]`);
                if (selectedTable) {
                    selectedTable.classList.add('selected');
                }
            }

            // Enable/disable request button
            const requestBtn = document.getElementById('requestWaiterBtn');
            requestBtn.disabled = !selectedTableId;
        }

        async function submitWaiterRequest() {
            if (!selectedTableId) {
                alert('Please select a table first.');
                return;
            }

            // Check if this is for a pending action (like submitting order)
            if (window.pendingAction === 'submitOrder') {
                // Store the selected table ID before closing modal
                const tableId = selectedTableId;
                toggleWaiterModal();
                window.pendingAction = null;
                // Ensure table is still selected after modal close
                selectedTableId = tableId;
                submitOrder();
                return;
            }

            const requestBtn = document.getElementById('requestWaiterBtn');
            requestBtn.disabled = true;
            requestBtn.textContent = 'Requesting...';

            try {
                // Extract tenant name from current URL
                const pathParts = window.location.pathname.split('/');
                const tenantName = pathParts[pathParts.indexOf('restaurant') + 1] || restaurantData.tenantName;
                
                console.log('Tenant name:', tenantName);
                console.log('Branch ID:', restaurantData.branchId);
                console.log('Table ID:', selectedTableId);
                
                const requestData = {
                    table_id: selectedTableId,
                    branch_id: restaurantData.branchId,
                    request_type: 'service',
                    notes: 'Customer requested waiter assistance'
                };
                
                console.log('Request data:', requestData);
                
                const response = await fetch(`/restaurant/${tenantName}/request-waiter`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    },
                    body: JSON.stringify(requestData)
                });

                console.log('Response status:', response.status);
                const result = await response.json();
                console.log('Response data:', result);

                if (response.ok && result.success) {
                    const selectedTable = restaurantData.tables.find(t => t.id === selectedTableId);
                    const tableNumber = selectedTable ? selectedTable.table_number : selectedTableId;
                    
                    showNotification(`Waiter has been called to table ${tableNumber}!`);
                    toggleWaiterModal();
                } else {
                    throw new Error(result.message || result.error || 'Failed to request waiter');
                }
            } catch (error) {
                console.error('Error requesting waiter:', error);
                alert('Failed to request waiter. Please try again. Error: ' + error.message);
            } finally {
                requestBtn.disabled = false;
                requestBtn.textContent = 'Request Waiter';
            }
        }

        function showNotification(message) {
            // Simple notification - you could enhance this with a proper toast system
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                background: var(--secondary-color);
                color: white;
                padding: 1rem;
                border-radius: 5px;
                z-index: 3000;
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Carousel functionality
        function startCarousel() {
            setInterval(() => {
                nextSlide();
            }, 5000);
        }

        function nextSlide() {
            const carousel = document.getElementById('carouselInner');
            const items = carousel.children;
            currentSlide = (currentSlide + 1) % items.length;
            carousel.style.transform = `translateX(-${currentSlide * 100}%)`;
        }

        function prevSlide() {
            const carousel = document.getElementById('carouselInner');
            const items = carousel.children;
            currentSlide = currentSlide === 0 ? items.length - 1 : currentSlide - 1;
            carousel.style.transform = `translateX(-${currentSlide * 100}%)`;
        }

        // Add CSS animation for notifications
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>